#SPDX-License-Identifier: MIT-0
---
# tasks file for roles/k8s

- block: 
  - name: Copy rke2-artifacts into remote server
    synchronize:
      src: rke2-artifacts
      dest: /root
      recursive: yes
      rsync_opts:
        - "--chown=root:root"
  tags: [ sync_rke2_artifacts ]
  
- block:
  - name: Install Rke2 on all nodes
    shell: |
      INSTALL_RKE2_ARTIFACT_PATH=/root/rke2-artifacts sh install.sh
    args:
      chdir: /root/rke2-artifacts
    register: result

  - name: Show result of install rke2
    debug:
      var: result
  tags: [ install_rke2 ]

- block: # Install and config keepalived for master only if have
  - name: Install and config keepalived 
    include_tasks: keepalived.yml
  when: inventory_hostname in groups['rke2-master'] and vip is defined and vip != ''
  tags: [ install_keepalived ]

- block:
  - name: Create rke2 config directory at {{ rke2_config_dir }}  
    file:
      path: '{{ rke2_config_dir }}'
      state: directory
      owner: root
      group: root
      mode: '0755'
  - name: Create Rke2 config.yml file
    template:
      src: 'rk2/config.yaml.j2'
      dest: '{{ rke2_config_dir }}/config.yaml'
      mode: '0644'
      owner: root
      group: root
  tags: [ config_rke2 ]

- name: Start Rke2 service
  systemd:
    name: "{{ 'rke2-server' if inventory_hostname in groups['rke2-master'] else 'rke2-agent' }}"
    state: started
    enabled: yes
  tags: [ start_rke2 ]

- block:
  - name: Copy kubectl
    shell: |
      cp /var/lib/rancher/rke2/bin/kubectl /usr/local/bin/kubectl
    register: result
  - name: Show result of running cp kubectl 
    debug:
      var: result
  - name: Create /root/.kube folder
    file:
      path: '/root/.kube'
      state: directory
      owner: root
      group: root
      mode: '0644'
      recurse: yes  # tạo cả thư mục cha nếu chưa tồn tại
  - name: Copy rke2 config rke2.yaml
    shell: |
      cp /etc/rancher/rke2/rke2.yaml /root/.kube/config
    register: result
  - name: Show result of running cp kubectl 
    debug:
      var: result
  tags: [ postinstall_rke2 ]
