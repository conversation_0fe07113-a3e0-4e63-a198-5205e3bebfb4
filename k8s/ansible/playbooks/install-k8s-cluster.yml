---
- name: Install K8s cluster
  hosts: node-master-01,node-master-02
  become: true
  gather_facts: true
  pre_tasks:
    - include_vars: ../group_vars/all/vars.yml
      tags: [ always ]

  tasks:
    - name: Debug
      debug:
        msg: "playbook_dir: {{ playbook_dir }}, inventory_hostname: {{ inventory_hostname }}"
      tags: [ debug ]

    - name: Debug
      debug:
        msg: "Proxy is SET to '{{ proxy }}'"
      when: (proxy | default('')) | trim != ""


    - name: Debug
      debug:
        msg: "Proxy is not SET"
      when: (proxy | default('')) | trim == ""


  roles:
    - rke2
    - helm
