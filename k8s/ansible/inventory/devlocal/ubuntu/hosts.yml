all:
  children:
    rke2-master:
      hosts:
        node-master-01:
          ansible_host: **************
          node_ip: **************
          node_name: node-master-01
          node_type: master
          rke2_tls_san: ["node-master-01", "**************"]
        node-master-02:
          ansible_host: **************
          node_ip: **************
          node_name: node-master-02
          node_type: master
          rke2_tls_san: ["node-master-02", "**************"]
        node-master-03:
          ansible_host: **************
          node_ip: **************
          node_name: node-master-03
          node_type: master
          rke2_tls_san: ["node-master-03", "**************"]

    rke2-worker:
      hosts:
        node-worker-01:
          ansible_host: **************
          node_ip: **************
          node_name: node-worker-01
          node_type: worker
        node-worker-02:
          ansible_host: **************
          node_ip: **************
          node_name: node-worker-02
          node_type: worker
        node-worker-03:
          ansible_host: **************
          node_ip: **************
          node_name: node-worker-03
          node_type: worker
  vars:
    # tell every SSH connection in this play to skip host‑key checking
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
    ansible_user: vagrant
